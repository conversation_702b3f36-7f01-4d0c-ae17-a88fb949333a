<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author><PERSON><PERSON><PERSON></author>
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1280</width>
    <height>720</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>940</width>
    <height>560</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="styleSheet">
   <property name="font">
    <font>
     <family>Segoe UI</family>
     <pointsize>10</pointsize>
     <italic>false</italic>
     <bold>false</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">/* /////////////////////////////////////////////////////////////////////////////////////////////////

SET APP STYLESHEET - FULL STYLES HERE
DARK THEME - DRACULA COLOR BASED

///////////////////////////////////////////////////////////////////////////////////////////////// */

QWidget{
	color: rgb(221, 221, 221);
	font: 10pt &quot;Segoe UI&quot;;
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Tooltip */
QToolTip {
	color: #ffffff;
	background-color: rgba(33, 37, 43, 180);
	border: 1px solid rgb(44, 49, 58);
	background-image: none;
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 2px solid rgb(255, 121, 198);
	text-align: left;
	padding-left: 8px;
	margin: 0px;
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Bg App */
#bgApp {	
	background-color: rgb(40, 44, 52);
	border: 1px solid rgb(44, 49, 58);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Left Menu */
#leftMenuBg {	
	background-color: rgb(33, 37, 43);
}
#topLogo {
	background-color: rgb(33, 37, 43);
	background-image: url(:/images/images/images/PyDracula.png);
	background-position: centered;
	background-repeat: no-repeat;
}
#titleLeftApp { font: 63 12pt &quot;Segoe UI Semibold&quot;; }
#titleLeftDescription { font: 8pt &quot;Segoe UI&quot;; color: rgb(189, 147, 249); }

/* MENUS */
#topMenu .QPushButton {	
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 22px solid transparent;
	background-color: transparent;
	text-align: left;
	padding-left: 44px;
}
#topMenu .QPushButton:hover {
	background-color: rgb(40, 44, 52);
}
#topMenu .QPushButton:pressed {	
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}
#bottomMenu .QPushButton {	
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 20px solid transparent;
	background-color:transparent;
	text-align: left;
	padding-left: 44px;
}
#bottomMenu .QPushButton:hover {
	background-color: rgb(40, 44, 52);
}
#bottomMenu .QPushButton:pressed {	
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}
#leftMenuFrame{
	border-top: 3px solid rgb(44, 49, 58);
}

/* Toggle Button */
#toggleButton {
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 20px solid transparent;
	background-color: rgb(37, 41, 48);
	text-align: left;
	padding-left: 44px;
	color: rgb(113, 126, 149);
}
#toggleButton:hover {
	background-color: rgb(40, 44, 52);
}
#toggleButton:pressed {
	background-color: rgb(189, 147, 249);
}

/* Title Menu */
#titleRightInfo { padding-left: 10px; }


/* /////////////////////////////////////////////////////////////////////////////////////////////////
Extra Tab */
#extraLeftBox {	
	background-color: rgb(44, 49, 58);
}
#extraTopBg{	
	background-color: rgb(189, 147, 249)
}

/* Icon */
#extraIcon {
	background-position: center;
	background-repeat: no-repeat;
	background-image: url(:/icons/images/icons/icon_settings.png);
}

/* Label */
#extraLabel { color: rgb(255, 255, 255); }

/* Btn Close */
#extraCloseColumnBtn { background-color: rgba(255, 255, 255, 0); border: none;  border-radius: 5px; }
#extraCloseColumnBtn:hover { background-color: rgb(196, 161, 249); border-style: solid; border-radius: 4px; }
#extraCloseColumnBtn:pressed { background-color: rgb(180, 141, 238); border-style: solid; border-radius: 4px; }

/* Extra Content */
#extraContent{
	border-top: 3px solid rgb(40, 44, 52);
}

/* Extra Top Menus */
#extraTopMenu .QPushButton {
background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 22px solid transparent;
	background-color:transparent;
	text-align: left;
	padding-left: 44px;
}
#extraTopMenu .QPushButton:hover {
	background-color: rgb(40, 44, 52);
}
#extraTopMenu .QPushButton:pressed {	
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Content App */
#contentTopBg{	
	background-color: rgb(33, 37, 43);
}
#contentBottom{
	border-top: 3px solid rgb(44, 49, 58);
}

/* Top Buttons */
#rightButtons .QPushButton { background-color: rgba(255, 255, 255, 0); border: none;  border-radius: 5px; }
#rightButtons .QPushButton:hover { background-color: rgb(44, 49, 57); border-style: solid; border-radius: 4px; }
#rightButtons .QPushButton:pressed { background-color: rgb(23, 26, 30); border-style: solid; border-radius: 4px; }

/* Theme Settings */
#extraRightBox { background-color: rgb(44, 49, 58); }
#themeSettingsTopDetail { background-color: rgb(189, 147, 249); }

/* Bottom Bar */
#bottomBar { background-color: rgb(44, 49, 58); }
#bottomBar QLabel { font-size: 11px; color: rgb(113, 126, 149); padding-left: 10px; padding-right: 10px; padding-bottom: 2px; }

/* CONTENT SETTINGS */
/* MENUS */
#contentSettings .QPushButton {	
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 22px solid transparent;
	background-color:transparent;
	text-align: left;
	padding-left: 44px;
}
#contentSettings .QPushButton:hover {
	background-color: rgb(40, 44, 52);
}
#contentSettings .QPushButton:pressed {	
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
QTableWidget */
QTableWidget {	
	background-color: transparent;
	padding: 10px;
	border-radius: 5px;
	gridline-color: rgb(44, 49, 58);
	border-bottom: 1px solid rgb(44, 49, 60);
}
QTableWidget::item{
	border-color: rgb(44, 49, 60);
	padding-left: 5px;
	padding-right: 5px;
	gridline-color: rgb(44, 49, 60);
}
QTableWidget::item:selected{
	background-color: rgb(189, 147, 249);
}
QHeaderView::section{
	background-color: rgb(33, 37, 43);
	max-width: 30px;
	border: 1px solid rgb(44, 49, 58);
	border-style: none;
    border-bottom: 1px solid rgb(44, 49, 60);
    border-right: 1px solid rgb(44, 49, 60);
}
QTableWidget::horizontalHeader {	
	background-color: rgb(33, 37, 43);
}
QHeaderView::section:horizontal
{
    border: 1px solid rgb(33, 37, 43);
	background-color: rgb(33, 37, 43);
	padding: 3px;
	border-top-left-radius: 7px;
    border-top-right-radius: 7px;
}
QHeaderView::section:vertical
{
    border: 1px solid rgb(44, 49, 60);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
LineEdit */
QLineEdit {
	background-color: rgb(33, 37, 43);
	border-radius: 5px;
	border: 2px solid rgb(33, 37, 43);
	padding-left: 10px;
	selection-color: rgb(255, 255, 255);
	selection-background-color: rgb(255, 121, 198);
}
QLineEdit:hover {
	border: 2px solid rgb(64, 71, 88);
}
QLineEdit:focus {
	border: 2px solid rgb(91, 101, 124);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
PlainTextEdit */
QPlainTextEdit {
	background-color: rgb(27, 29, 35);
	border-radius: 5px;
	padding: 10px;
	selection-color: rgb(255, 255, 255);
	selection-background-color: rgb(255, 121, 198);
}
QPlainTextEdit  QScrollBar:vertical {
    width: 8px;
 }
QPlainTextEdit  QScrollBar:horizontal {
    height: 8px;
 }
QPlainTextEdit:hover {
	border: 2px solid rgb(64, 71, 88);
}
QPlainTextEdit:focus {
	border: 2px solid rgb(91, 101, 124);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
ScrollBars */
QScrollBar:horizontal {
    border: none;
    background: rgb(52, 59, 72);
    height: 8px;
    margin: 0px 21px 0 21px;
	border-radius: 0px;
}
QScrollBar::handle:horizontal {
    background: rgb(189, 147, 249);
    min-width: 25px;
	border-radius: 4px
}
QScrollBar::add-line:horizontal {
    border: none;
    background: rgb(55, 63, 77);
    width: 20px;
	border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    subcontrol-position: right;
    subcontrol-origin: margin;
}
QScrollBar::sub-line:horizontal {
    border: none;
    background: rgb(55, 63, 77);
    width: 20px;
	border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    subcontrol-position: left;
    subcontrol-origin: margin;
}
QScrollBar::up-arrow:horizontal, QScrollBar::down-arrow:horizontal
{
     background: none;
}
QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal
{
     background: none;
}
 QScrollBar:vertical {
	border: none;
    background: rgb(52, 59, 72);
    width: 8px;
    margin: 21px 0 21px 0;
	border-radius: 0px;
 }
 QScrollBar::handle:vertical {	
	background: rgb(189, 147, 249);
    min-height: 25px;
	border-radius: 4px
 }
 QScrollBar::add-line:vertical {
     border: none;
    background: rgb(55, 63, 77);
     height: 20px;
	border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
     subcontrol-position: bottom;
     subcontrol-origin: margin;
 }
 QScrollBar::sub-line:vertical {
	border: none;
    background: rgb(55, 63, 77);
     height: 20px;
	border-top-left-radius: 4px;
    border-top-right-radius: 4px;
     subcontrol-position: top;
     subcontrol-origin: margin;
 }
 QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
     background: none;
 }

 QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
     background: none;
 }

/* /////////////////////////////////////////////////////////////////////////////////////////////////
CheckBox */
QCheckBox::indicator {
    border: 3px solid rgb(52, 59, 72);
	width: 15px;
	height: 15px;
	border-radius: 10px;
    background: rgb(44, 49, 60);
}
QCheckBox::indicator:hover {
    border: 3px solid rgb(58, 66, 81);
}
QCheckBox::indicator:checked {
    background: 3px solid rgb(52, 59, 72);
	border: 3px solid rgb(52, 59, 72);	
	background-image: url(:/icons/images/icons/cil-check-alt.png);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
RadioButton */
QRadioButton::indicator {
    border: 3px solid rgb(52, 59, 72);
	width: 15px;
	height: 15px;
	border-radius: 10px;
    background: rgb(44, 49, 60);
}
QRadioButton::indicator:hover {
    border: 3px solid rgb(58, 66, 81);
}
QRadioButton::indicator:checked {
    background: 3px solid rgb(94, 106, 130);
	border: 3px solid rgb(52, 59, 72);	
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
ComboBox */
QComboBox{
	background-color: rgb(27, 29, 35);
	border-radius: 5px;
	border: 2px solid rgb(33, 37, 43);
	padding: 5px;
	padding-left: 10px;
}
QComboBox:hover{
	border: 2px solid rgb(64, 71, 88);
}
QComboBox::drop-down {
	subcontrol-origin: padding;
	subcontrol-position: top right;
	width: 25px; 
	border-left-width: 3px;
	border-left-color: rgba(39, 44, 54, 150);
	border-left-style: solid;
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;	
	background-image: url(:/icons/images/icons/cil-arrow-bottom.png);
	background-position: center;
	background-repeat: no-reperat;
 }
QComboBox QAbstractItemView {
	color: rgb(255, 121, 198);	
	background-color: rgb(33, 37, 43);
	padding: 10px;
	selection-background-color: rgb(39, 44, 54);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Sliders */
QSlider::groove:horizontal {
    border-radius: 5px;
    height: 10px;
	margin: 0px;
	background-color: rgb(52, 59, 72);
}
QSlider::groove:horizontal:hover {
	background-color: rgb(55, 62, 76);
}
QSlider::handle:horizontal {
    background-color: rgb(189, 147, 249);
    border: none;
    height: 10px;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}
QSlider::handle:horizontal:hover {
    background-color: rgb(195, 155, 255);
}
QSlider::handle:horizontal:pressed {
    background-color: rgb(255, 121, 198);
}

QSlider::groove:vertical {
    border-radius: 5px;
    width: 10px;
    margin: 0px;
	background-color: rgb(52, 59, 72);
}
QSlider::groove:vertical:hover {
	background-color: rgb(55, 62, 76);
}
QSlider::handle:vertical {
    background-color: rgb(189, 147, 249);
	border: none;
    height: 10px;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}
QSlider::handle:vertical:hover {
    background-color: rgb(195, 155, 255);
}
QSlider::handle:vertical:pressed {
    background-color: rgb(255, 121, 198);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
CommandLinkButton */
QCommandLinkButton {	
	color: rgb(255, 121, 198);
	border-radius: 5px;
	padding: 5px;
	color: rgb(255, 170, 255);
}
QCommandLinkButton:hover {	
	color: rgb(255, 170, 255);
	background-color: rgb(44, 49, 60);
}
QCommandLinkButton:pressed {	
	color: rgb(189, 147, 249);
	background-color: rgb(52, 58, 71);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Button */
#pagesContainer QPushButton {
	border: 2px solid rgb(52, 59, 72);
	border-radius: 5px;	
	background-color: rgb(52, 59, 72);
}
#pagesContainer QPushButton:hover {
	background-color: rgb(57, 65, 80);
	border: 2px solid rgb(61, 70, 86);
}
#pagesContainer QPushButton:pressed {	
	background-color: rgb(35, 40, 49);
	border: 2px solid rgb(43, 50, 61);
}

</string>
   </property>
   <layout class="QVBoxLayout" name="appMargins">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>10</number>
    </property>
    <property name="topMargin">
     <number>10</number>
    </property>
    <property name="rightMargin">
     <number>10</number>
    </property>
    <property name="bottomMargin">
     <number>10</number>
    </property>
    <item>
     <widget class="QFrame" name="bgApp">
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QHBoxLayout" name="appLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QFrame" name="leftMenuBg">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>60</width>
           <height>********</height>
          </size>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QFrame" name="topLogoInfo">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>50</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>********</width>
              <height>50</height>
             </size>
            </property>
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <widget class="QFrame" name="topLogo">
             <property name="geometry">
              <rect>
               <x>10</x>
               <y>5</y>
               <width>42</width>
               <height>42</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>42</width>
               <height>42</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>42</width>
               <height>42</height>
              </size>
             </property>
             <property name="frameShape">
              <enum>QFrame::NoFrame</enum>
             </property>
             <property name="frameShadow">
              <enum>QFrame::Raised</enum>
             </property>
            </widget>
            <widget class="QLabel" name="titleLeftApp">
             <property name="geometry">
              <rect>
               <x>70</x>
               <y>8</y>
               <width>160</width>
               <height>20</height>
              </rect>
             </property>
             <property name="font">
              <font>
               <family>Segoe UI Semibold</family>
               <pointsize>12</pointsize>
               <italic>false</italic>
               <bold>false</bold>
              </font>
             </property>
             <property name="text">
              <string>PyDracula</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
             </property>
            </widget>
            <widget class="QLabel" name="titleLeftDescription">
             <property name="geometry">
              <rect>
               <x>70</x>
               <y>27</y>
               <width>160</width>
               <height>16</height>
              </rect>
             </property>
             <property name="maximumSize">
              <size>
               <width>********</width>
               <height>16</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Segoe UI</family>
               <pointsize>8</pointsize>
               <italic>false</italic>
               <bold>false</bold>
              </font>
             </property>
             <property name="text">
              <string>Modern GUI / Flat Style</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
             </property>
            </widget>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="leftMenuFrame">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalMenuLayout">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QFrame" name="toggleBox">
               <property name="maximumSize">
                <size>
                 <width>********</width>
                 <height>45</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_4">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QPushButton" name="toggleButton">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Segoe UI</family>
                    <pointsize>10</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/icon_menu.png);</string>
                  </property>
                  <property name="text">
                   <string>Hide</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item alignment="Qt::AlignTop">
              <widget class="QFrame" name="topMenu">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_8">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QPushButton" name="btn_home">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Segoe UI</family>
                    <pointsize>10</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-home.png);</string>
                  </property>
                  <property name="text">
                   <string>Home</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_widgets">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Segoe UI</family>
                    <pointsize>10</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-gamepad.png);</string>
                  </property>
                  <property name="text">
                   <string>Widgets</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_new">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Segoe UI</family>
                    <pointsize>10</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-file.png);</string>
                  </property>
                  <property name="text">
                   <string>New</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_save">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Segoe UI</family>
                    <pointsize>10</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-save.png)</string>
                  </property>
                  <property name="text">
                   <string>Save</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_exit">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Segoe UI</family>
                    <pointsize>10</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-x.png);</string>
                  </property>
                  <property name="text">
                   <string>Exit</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item alignment="Qt::AlignBottom">
              <widget class="QFrame" name="bottomMenu">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_9">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QPushButton" name="toggleLeftBox">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Segoe UI</family>
                    <pointsize>10</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/icon_settings.png);</string>
                  </property>
                  <property name="text">
                   <string>Left Box</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="extraLeftBox">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>0</width>
           <height>********</height>
          </size>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="extraColumLayout">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QFrame" name="extraTopBg">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>50</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>********</width>
              <height>50</height>
             </size>
            </property>
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_5">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <layout class="QGridLayout" name="extraTopLayout">
               <property name="leftMargin">
                <number>10</number>
               </property>
               <property name="rightMargin">
                <number>10</number>
               </property>
               <property name="horizontalSpacing">
                <number>10</number>
               </property>
               <property name="verticalSpacing">
                <number>0</number>
               </property>
               <item row="0" column="0">
                <widget class="QFrame" name="extraIcon">
                 <property name="minimumSize">
                  <size>
                   <width>20</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>20</width>
                   <height>20</height>
                  </size>
                 </property>
                 <property name="frameShape">
                  <enum>QFrame::NoFrame</enum>
                 </property>
                 <property name="frameShadow">
                  <enum>QFrame::Raised</enum>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QLabel" name="extraLabel">
                 <property name="minimumSize">
                  <size>
                   <width>150</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="text">
                  <string>Left Box</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="2">
                <widget class="QPushButton" name="extraCloseColumnBtn">
                 <property name="minimumSize">
                  <size>
                   <width>28</width>
                   <height>28</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>28</width>
                   <height>28</height>
                  </size>
                 </property>
                 <property name="cursor">
                  <cursorShape>PointingHandCursor</cursorShape>
                 </property>
                 <property name="toolTip">
                  <string>Close left box</string>
                 </property>
                 <property name="text">
                  <string/>
                 </property>
                 <property name="icon">
                  <iconset resource="resources.qrc">
                   <normaloff>:/icons/images/icons/icon_close.png</normaloff>:/icons/images/icons/icon_close.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>20</width>
                   <height>20</height>
                  </size>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="extraContent">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_12">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item alignment="Qt::AlignTop">
              <widget class="QFrame" name="extraTopMenu">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_11">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QPushButton" name="btn_share">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Segoe UI</family>
                    <pointsize>10</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-share-boxed.png);</string>
                  </property>
                  <property name="text">
                   <string>Share</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_adjustments">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Segoe UI</family>
                    <pointsize>10</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-equalizer.png);</string>
                  </property>
                  <property name="text">
                   <string>Adjustments</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_more">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Segoe UI</family>
                    <pointsize>10</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-layers.png);</string>
                  </property>
                  <property name="text">
                   <string>More</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="extraCenter">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_10">
                <item>
                 <widget class="QTextEdit" name="textEdit">
                  <property name="minimumSize">
                   <size>
                    <width>222</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background: transparent;</string>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="readOnly">
                   <bool>true</bool>
                  </property>
                  <property name="html">
                   <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Segoe UI'; font-size:10pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:12pt; font-weight:600; color:#ff79c6;&quot;&gt;PyDracula&lt;/span&gt;&lt;/p&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; color:#ffffff;&quot;&gt;An interface created using Python and PySide (support for PyQt), and with colors based on the Dracula theme created by Zeno Rocha.&lt;/span&gt;&lt;/p&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; color:#ffffff;&quot;&gt;MIT License&lt;/span&gt;&lt;/p&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; color:#bd93f9;&quot;&gt;Created by: Wanderson M. Pimenta&lt;/span&gt;&lt;/p&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:12pt; font-weight:600; color:#ff79c6;&quot;&gt;Convert UI&lt;/span&gt;&lt;/p&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:9pt; color:#ffffff;&quot;&gt;pyside6-uic main.ui &amp;gt; ui_main.py&lt;/span&gt;&lt;/p&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:12pt; font-weight:600; color:#ff79c6;&quot;&gt;Convert QRC&lt;/span&gt;&lt;/p&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:9pt; color:#ffffff;&quot;&gt;pyside6-rcc resources.qrc -o resources_rc.py&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="extraBottom">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="contentBox">
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QFrame" name="contentTopBg">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>50</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>********</width>
              <height>50</height>
             </size>
            </property>
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>10</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QFrame" name="leftBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_3">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="titleRightInfo">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>********</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Segoe UI</family>
                    <pointsize>10</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>PyDracula APP - Theme with colors based on Dracula for Python.</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item alignment="Qt::AlignRight">
              <widget class="QFrame" name="rightButtons">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>28</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_2">
                <property name="spacing">
                 <number>5</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QPushButton" name="settingsTopBtn">
                  <property name="minimumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="toolTip">
                   <string>Settings</string>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset resource="resources.qrc">
                    <normaloff>:/icons/images/icons/icon_settings.png</normaloff>:/icons/images/icons/icon_settings.png</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="minimizeAppBtn">
                  <property name="minimumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="toolTip">
                   <string>Minimize</string>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset resource="resources.qrc">
                    <normaloff>:/icons/images/icons/icon_minimize.png</normaloff>:/icons/images/icons/icon_minimize.png</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="maximizeRestoreAppBtn">
                  <property name="minimumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Segoe UI</family>
                    <pointsize>10</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                    <stylestrategy>PreferDefault</stylestrategy>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="toolTip">
                   <string>Maximize</string>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset resource="resources.qrc">
                    <normaloff>:/icons/images/icons/icon_maximize.png</normaloff>:/icons/images/icons/icon_maximize.png</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="closeAppBtn">
                  <property name="minimumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="toolTip">
                   <string>Close</string>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset resource="resources.qrc">
                    <normaloff>:/icons/images/icons/icon_close.png</normaloff>:/icons/images/icons/icon_close.png</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="contentBottom">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_6">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QFrame" name="content">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_4">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QFrame" name="pagesContainer">
                  <property name="styleSheet">
                   <string notr="true"/>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                  <layout class="QVBoxLayout" name="verticalLayout_15">
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <property name="leftMargin">
                    <number>10</number>
                   </property>
                   <property name="topMargin">
                    <number>10</number>
                   </property>
                   <property name="rightMargin">
                    <number>10</number>
                   </property>
                   <property name="bottomMargin">
                    <number>10</number>
                   </property>
                   <item>
                    <widget class="QStackedWidget" name="stackedWidget">
                     <property name="styleSheet">
                      <string notr="true">background: transparent;</string>
                     </property>
                     <property name="currentIndex">
                      <number>2</number>
                     </property>
                     <widget class="QWidget" name="home">
                      <property name="styleSheet">
                       <string notr="true">background-image: url(:/images/images/images/PyDracula_vertical.png);
background-position: center;
background-repeat: no-repeat;</string>
                      </property>
                     </widget>
                     <widget class="QWidget" name="widgets">
                      <property name="styleSheet">
                       <string notr="true">b</string>
                      </property>
                      <layout class="QVBoxLayout" name="verticalLayout">
                       <property name="spacing">
                        <number>10</number>
                       </property>
                       <property name="leftMargin">
                        <number>10</number>
                       </property>
                       <property name="topMargin">
                        <number>10</number>
                       </property>
                       <property name="rightMargin">
                        <number>10</number>
                       </property>
                       <property name="bottomMargin">
                        <number>10</number>
                       </property>
                       <item>
                        <widget class="QFrame" name="row_1">
                         <property name="frameShape">
                          <enum>QFrame::StyledPanel</enum>
                         </property>
                         <property name="frameShadow">
                          <enum>QFrame::Raised</enum>
                         </property>
                         <layout class="QVBoxLayout" name="verticalLayout_16">
                          <property name="spacing">
                           <number>0</number>
                          </property>
                          <property name="leftMargin">
                           <number>0</number>
                          </property>
                          <property name="topMargin">
                           <number>0</number>
                          </property>
                          <property name="rightMargin">
                           <number>0</number>
                          </property>
                          <property name="bottomMargin">
                           <number>0</number>
                          </property>
                          <item>
                           <widget class="QFrame" name="frame_div_content_1">
                            <property name="minimumSize">
                             <size>
                              <width>0</width>
                              <height>110</height>
                             </size>
                            </property>
                            <property name="maximumSize">
                             <size>
                              <width>********</width>
                              <height>110</height>
                             </size>
                            </property>
                            <property name="frameShape">
                             <enum>QFrame::NoFrame</enum>
                            </property>
                            <property name="frameShadow">
                             <enum>QFrame::Raised</enum>
                            </property>
                            <layout class="QVBoxLayout" name="verticalLayout_17">
                             <property name="spacing">
                              <number>0</number>
                             </property>
                             <property name="leftMargin">
                              <number>0</number>
                             </property>
                             <property name="topMargin">
                              <number>0</number>
                             </property>
                             <property name="rightMargin">
                              <number>0</number>
                             </property>
                             <property name="bottomMargin">
                              <number>0</number>
                             </property>
                             <item>
                              <widget class="QFrame" name="frame_title_wid_1">
                               <property name="maximumSize">
                                <size>
                                 <width>********</width>
                                 <height>35</height>
                                </size>
                               </property>
                               <property name="frameShape">
                                <enum>QFrame::StyledPanel</enum>
                               </property>
                               <property name="frameShadow">
                                <enum>QFrame::Raised</enum>
                               </property>
                               <layout class="QVBoxLayout" name="verticalLayout_18">
                                <item>
                                 <widget class="QLabel" name="labelBoxBlenderInstalation">
                                  <property name="font">
                                   <font>
                                    <family>Segoe UI</family>
                                    <pointsize>10</pointsize>
                                    <italic>false</italic>
                                    <bold>false</bold>
                                   </font>
                                  </property>
                                  <property name="styleSheet">
                                   <string notr="true"/>
                                  </property>
                                  <property name="text">
                                   <string>FILE BOX</string>
                                  </property>
                                 </widget>
                                </item>
                               </layout>
                              </widget>
                             </item>
                             <item>
                              <widget class="QFrame" name="frame_content_wid_1">
                               <property name="frameShape">
                                <enum>QFrame::NoFrame</enum>
                               </property>
                               <property name="frameShadow">
                                <enum>QFrame::Raised</enum>
                               </property>
                               <layout class="QHBoxLayout" name="horizontalLayout_9">
                                <item>
                                 <layout class="QGridLayout" name="gridLayout">
                                  <property name="bottomMargin">
                                   <number>0</number>
                                  </property>
                                  <item row="0" column="0">
                                   <widget class="QLineEdit" name="lineEdit">
                                    <property name="minimumSize">
                                     <size>
                                      <width>0</width>
                                      <height>30</height>
                                     </size>
                                    </property>
                                    <property name="styleSheet">
                                     <string notr="true">background-color: rgb(33, 37, 43);</string>
                                    </property>
                                    <property name="text">
                                     <string/>
                                    </property>
                                    <property name="placeholderText">
                                     <string>Type here</string>
                                    </property>
                                   </widget>
                                  </item>
                                  <item row="0" column="1">
                                   <widget class="QPushButton" name="pushButton">
                                    <property name="minimumSize">
                                     <size>
                                      <width>150</width>
                                      <height>30</height>
                                     </size>
                                    </property>
                                    <property name="font">
                                     <font>
                                      <family>Segoe UI</family>
                                      <pointsize>10</pointsize>
                                      <italic>false</italic>
                                      <bold>false</bold>
                                     </font>
                                    </property>
                                    <property name="cursor">
                                     <cursorShape>PointingHandCursor</cursorShape>
                                    </property>
                                    <property name="styleSheet">
                                     <string notr="true">background-color: rgb(52, 59, 72);</string>
                                    </property>
                                    <property name="text">
                                     <string>Open</string>
                                    </property>
                                    <property name="icon">
                                     <iconset resource="resources.qrc">
                                      <normaloff>:/icons/images/icons/cil-folder-open.png</normaloff>:/icons/images/icons/cil-folder-open.png</iconset>
                                    </property>
                                   </widget>
                                  </item>
                                  <item row="1" column="0" colspan="2">
                                   <widget class="QLabel" name="labelVersion_3">
                                    <property name="styleSheet">
                                     <string notr="true">color: rgb(113, 126, 149);</string>
                                    </property>
                                    <property name="lineWidth">
                                     <number>1</number>
                                    </property>
                                    <property name="text">
                                     <string>Label description</string>
                                    </property>
                                    <property name="alignment">
                                     <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                                    </property>
                                   </widget>
                                  </item>
                                 </layout>
                                </item>
                               </layout>
                              </widget>
                             </item>
                            </layout>
                           </widget>
                          </item>
                         </layout>
                        </widget>
                       </item>
                       <item>
                        <widget class="QFrame" name="row_2">
                         <property name="minimumSize">
                          <size>
                           <width>0</width>
                           <height>150</height>
                          </size>
                         </property>
                         <property name="frameShape">
                          <enum>QFrame::StyledPanel</enum>
                         </property>
                         <property name="frameShadow">
                          <enum>QFrame::Raised</enum>
                         </property>
                         <layout class="QVBoxLayout" name="verticalLayout_19">
                          <item>
                           <layout class="QGridLayout" name="gridLayout_2">
                            <item row="0" column="0">
                             <widget class="QCheckBox" name="checkBox">
                              <property name="autoFillBackground">
                               <bool>false</bool>
                              </property>
                              <property name="styleSheet">
                               <string notr="true"/>
                              </property>
                              <property name="text">
                               <string>CheckBox</string>
                              </property>
                             </widget>
                            </item>
                            <item row="0" column="1">
                             <widget class="QRadioButton" name="radioButton">
                              <property name="styleSheet">
                               <string notr="true"/>
                              </property>
                              <property name="text">
                               <string>RadioButton</string>
                              </property>
                             </widget>
                            </item>
                            <item row="0" column="2" rowspan="3">
                             <widget class="QSlider" name="verticalSlider">
                              <property name="styleSheet">
                               <string notr="true"/>
                              </property>
                              <property name="orientation">
                               <enum>Qt::Vertical</enum>
                              </property>
                             </widget>
                            </item>
                            <item row="0" column="4" rowspan="3">
                             <widget class="QScrollBar" name="verticalScrollBar">
                              <property name="styleSheet">
                               <string notr="true"> QScrollBar:vertical { background: rgb(52, 59, 72); }
 QScrollBar:horizontal { background: rgb(52, 59, 72); }</string>
                              </property>
                              <property name="orientation">
                               <enum>Qt::Vertical</enum>
                              </property>
                             </widget>
                            </item>
                            <item row="0" column="5" rowspan="3">
                             <widget class="QScrollArea" name="scrollArea">
                              <property name="styleSheet">
                               <string notr="true"> QScrollBar:vertical {
    background: rgb(52, 59, 72);
 }
 QScrollBar:horizontal {
    background: rgb(52, 59, 72);
 }</string>
                              </property>
                              <property name="frameShape">
                               <enum>QFrame::NoFrame</enum>
                              </property>
                              <property name="verticalScrollBarPolicy">
                               <enum>Qt::ScrollBarAlwaysOn</enum>
                              </property>
                              <property name="horizontalScrollBarPolicy">
                               <enum>Qt::ScrollBarAsNeeded</enum>
                              </property>
                              <property name="widgetResizable">
                               <bool>true</bool>
                              </property>
                              <widget class="QWidget" name="scrollAreaWidgetContents">
                               <property name="geometry">
                                <rect>
                                 <x>0</x>
                                 <y>0</y>
                                 <width>218</width>
                                 <height>218</height>
                                </rect>
                               </property>
                               <property name="styleSheet">
                                <string notr="true"> QScrollBar:vertical {
	border: none;
    background: rgb(52, 59, 72);
    width: 14px;
    margin: 21px 0 21px 0;
	border-radius: 0px;
 }</string>
                               </property>
                               <layout class="QHBoxLayout" name="horizontalLayout_11">
                                <item>
                                 <widget class="QPlainTextEdit" name="plainTextEdit">
                                  <property name="minimumSize">
                                   <size>
                                    <width>200</width>
                                    <height>200</height>
                                   </size>
                                  </property>
                                  <property name="styleSheet">
                                   <string notr="true">background-color: rgb(33, 37, 43);</string>
                                  </property>
                                 </widget>
                                </item>
                               </layout>
                              </widget>
                             </widget>
                            </item>
                            <item row="1" column="0" colspan="2">
                             <widget class="QComboBox" name="comboBox">
                              <property name="font">
                               <font>
                                <family>Segoe UI</family>
                                <pointsize>10</pointsize>
                                <italic>false</italic>
                                <bold>false</bold>
                               </font>
                              </property>
                              <property name="autoFillBackground">
                               <bool>false</bool>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">background-color: rgb(33, 37, 43);</string>
                              </property>
                              <property name="iconSize">
                               <size>
                                <width>16</width>
                                <height>16</height>
                               </size>
                              </property>
                              <property name="frame">
                               <bool>true</bool>
                              </property>
                              <item>
                               <property name="text">
                                <string>Test 1</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Test 2</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Test 3</string>
                               </property>
                              </item>
                             </widget>
                            </item>
                            <item row="1" column="3">
                             <widget class="QScrollBar" name="horizontalScrollBar">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="styleSheet">
                               <string notr="true"> QScrollBar:vertical { background: rgb(52, 59, 72); }
 QScrollBar:horizontal { background: rgb(52, 59, 72); }</string>
                              </property>
                              <property name="orientation">
                               <enum>Qt::Horizontal</enum>
                              </property>
                             </widget>
                            </item>
                            <item row="1" column="6">
                             <widget class="QCommandLinkButton" name="commandLinkButton">
                              <property name="cursor">
                               <cursorShape>PointingHandCursor</cursorShape>
                              </property>
                              <property name="styleSheet">
                               <string notr="true"/>
                              </property>
                              <property name="text">
                               <string>Link Button</string>
                              </property>
                              <property name="icon">
                               <iconset resource="resources.qrc">
                                <normaloff>:/icons/images/icons/cil-link.png</normaloff>:/icons/images/icons/cil-link.png</iconset>
                              </property>
                              <property name="description">
                               <string>Link description</string>
                              </property>
                             </widget>
                            </item>
                            <item row="2" column="0" colspan="2">
                             <widget class="QSlider" name="horizontalSlider">
                              <property name="styleSheet">
                               <string notr="true"/>
                              </property>
                              <property name="orientation">
                               <enum>Qt::Horizontal</enum>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </item>
                         </layout>
                        </widget>
                       </item>
                       <item>
                        <widget class="QFrame" name="row_3">
                         <property name="minimumSize">
                          <size>
                           <width>0</width>
                           <height>150</height>
                          </size>
                         </property>
                         <property name="frameShape">
                          <enum>QFrame::StyledPanel</enum>
                         </property>
                         <property name="frameShadow">
                          <enum>QFrame::Raised</enum>
                         </property>
                         <layout class="QHBoxLayout" name="horizontalLayout_12">
                          <property name="spacing">
                           <number>0</number>
                          </property>
                          <property name="leftMargin">
                           <number>0</number>
                          </property>
                          <property name="topMargin">
                           <number>0</number>
                          </property>
                          <property name="rightMargin">
                           <number>0</number>
                          </property>
                          <property name="bottomMargin">
                           <number>0</number>
                          </property>
                          <item>
                           <widget class="QTableWidget" name="tableWidget">
                            <property name="sizePolicy">
                             <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                              <horstretch>0</horstretch>
                              <verstretch>0</verstretch>
                             </sizepolicy>
                            </property>
                            <property name="palette">
                             <palette>
                              <active>
                               <colorrole role="WindowText">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="255">
                                  <red>221</red>
                                  <green>221</green>
                                  <blue>221</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="Button">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="0">
                                  <red>0</red>
                                  <green>0</green>
                                  <blue>0</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="Text">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="255">
                                  <red>221</red>
                                  <green>221</green>
                                  <blue>221</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="ButtonText">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="255">
                                  <red>221</red>
                                  <green>221</green>
                                  <blue>221</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="Base">
                                <brush brushstyle="NoBrush">
                                 <color alpha="255">
                                  <red>0</red>
                                  <green>0</green>
                                  <blue>0</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="Window">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="0">
                                  <red>0</red>
                                  <green>0</green>
                                  <blue>0</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="PlaceholderText">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="255">
                                  <red>221</red>
                                  <green>221</green>
                                  <blue>221</blue>
                                 </color>
                                </brush>
                               </colorrole>
                              </active>
                              <inactive>
                               <colorrole role="WindowText">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="255">
                                  <red>221</red>
                                  <green>221</green>
                                  <blue>221</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="Button">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="0">
                                  <red>0</red>
                                  <green>0</green>
                                  <blue>0</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="Text">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="255">
                                  <red>221</red>
                                  <green>221</green>
                                  <blue>221</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="ButtonText">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="255">
                                  <red>221</red>
                                  <green>221</green>
                                  <blue>221</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="Base">
                                <brush brushstyle="NoBrush">
                                 <color alpha="255">
                                  <red>0</red>
                                  <green>0</green>
                                  <blue>0</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="Window">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="0">
                                  <red>0</red>
                                  <green>0</green>
                                  <blue>0</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="PlaceholderText">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="255">
                                  <red>221</red>
                                  <green>221</green>
                                  <blue>221</blue>
                                 </color>
                                </brush>
                               </colorrole>
                              </inactive>
                              <disabled>
                               <colorrole role="WindowText">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="255">
                                  <red>221</red>
                                  <green>221</green>
                                  <blue>221</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="Button">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="0">
                                  <red>0</red>
                                  <green>0</green>
                                  <blue>0</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="Text">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="255">
                                  <red>221</red>
                                  <green>221</green>
                                  <blue>221</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="ButtonText">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="255">
                                  <red>221</red>
                                  <green>221</green>
                                  <blue>221</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="Base">
                                <brush brushstyle="NoBrush">
                                 <color alpha="255">
                                  <red>0</red>
                                  <green>0</green>
                                  <blue>0</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="Window">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="0">
                                  <red>0</red>
                                  <green>0</green>
                                  <blue>0</blue>
                                 </color>
                                </brush>
                               </colorrole>
                               <colorrole role="PlaceholderText">
                                <brush brushstyle="SolidPattern">
                                 <color alpha="255">
                                  <red>221</red>
                                  <green>221</green>
                                  <blue>221</blue>
                                 </color>
                                </brush>
                               </colorrole>
                              </disabled>
                             </palette>
                            </property>
                            <property name="frameShape">
                             <enum>QFrame::NoFrame</enum>
                            </property>
                            <property name="verticalScrollBarPolicy">
                             <enum>Qt::ScrollBarAlwaysOn</enum>
                            </property>
                            <property name="sizeAdjustPolicy">
                             <enum>QAbstractScrollArea::AdjustToContents</enum>
                            </property>
                            <property name="editTriggers">
                             <set>QAbstractItemView::NoEditTriggers</set>
                            </property>
                            <property name="selectionMode">
                             <enum>QAbstractItemView::SingleSelection</enum>
                            </property>
                            <property name="selectionBehavior">
                             <enum>QAbstractItemView::SelectRows</enum>
                            </property>
                            <property name="showGrid">
                             <bool>true</bool>
                            </property>
                            <property name="gridStyle">
                             <enum>Qt::SolidLine</enum>
                            </property>
                            <property name="sortingEnabled">
                             <bool>false</bool>
                            </property>
                            <attribute name="horizontalHeaderVisible">
                             <bool>false</bool>
                            </attribute>
                            <attribute name="horizontalHeaderCascadingSectionResizes">
                             <bool>true</bool>
                            </attribute>
                            <attribute name="horizontalHeaderDefaultSectionSize">
                             <number>200</number>
                            </attribute>
                            <attribute name="horizontalHeaderStretchLastSection">
                             <bool>true</bool>
                            </attribute>
                            <attribute name="verticalHeaderVisible">
                             <bool>false</bool>
                            </attribute>
                            <attribute name="verticalHeaderCascadingSectionResizes">
                             <bool>false</bool>
                            </attribute>
                            <attribute name="verticalHeaderHighlightSections">
                             <bool>false</bool>
                            </attribute>
                            <attribute name="verticalHeaderStretchLastSection">
                             <bool>true</bool>
                            </attribute>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                             <property name="font">
                              <font>
                               <family>Segoe UI</family>
                              </font>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <row>
                             <property name="text">
                              <string>New Row</string>
                             </property>
                            </row>
                            <column>
                             <property name="text">
                              <string>0</string>
                             </property>
                            </column>
                            <column>
                             <property name="text">
                              <string>1</string>
                             </property>
                            </column>
                            <column>
                             <property name="text">
                              <string>2</string>
                             </property>
                            </column>
                            <column>
                             <property name="text">
                              <string>3</string>
                             </property>
                            </column>
                            <item row="0" column="0">
                             <property name="text">
                              <string>Test</string>
                             </property>
                            </item>
                            <item row="0" column="1">
                             <property name="text">
                              <string>Text</string>
                             </property>
                            </item>
                            <item row="0" column="2">
                             <property name="text">
                              <string>Cell</string>
                             </property>
                            </item>
                            <item row="0" column="3">
                             <property name="text">
                              <string>Line</string>
                             </property>
                            </item>
                           </widget>
                          </item>
                         </layout>
                        </widget>
                       </item>
                      </layout>
                     </widget>
                     <widget class="QWidget" name="new_page">
                      <layout class="QVBoxLayout" name="verticalLayout_20">
                       <item>
                        <widget class="QLabel" name="label">
                         <property name="text">
                          <string>NEW PAGE TEST</string>
                         </property>
                         <property name="alignment">
                          <set>Qt::AlignCenter</set>
                         </property>
                        </widget>
                       </item>
                      </layout>
                     </widget>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QFrame" name="extraRightBox">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>0</width>
                    <height>********</height>
                   </size>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                  <layout class="QVBoxLayout" name="verticalLayout_7">
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <widget class="QFrame" name="themeSettingsTopDetail">
                     <property name="maximumSize">
                      <size>
                       <width>********</width>
                       <height>3</height>
                      </size>
                     </property>
                     <property name="frameShape">
                      <enum>QFrame::NoFrame</enum>
                     </property>
                     <property name="frameShadow">
                      <enum>QFrame::Raised</enum>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QFrame" name="contentSettings">
                     <property name="frameShape">
                      <enum>QFrame::NoFrame</enum>
                     </property>
                     <property name="frameShadow">
                      <enum>QFrame::Raised</enum>
                     </property>
                     <layout class="QVBoxLayout" name="verticalLayout_13">
                      <property name="spacing">
                       <number>0</number>
                      </property>
                      <property name="leftMargin">
                       <number>0</number>
                      </property>
                      <property name="topMargin">
                       <number>0</number>
                      </property>
                      <property name="rightMargin">
                       <number>0</number>
                      </property>
                      <property name="bottomMargin">
                       <number>0</number>
                      </property>
                      <item alignment="Qt::AlignTop">
                       <widget class="QFrame" name="topMenus">
                        <property name="frameShape">
                         <enum>QFrame::NoFrame</enum>
                        </property>
                        <property name="frameShadow">
                         <enum>QFrame::Raised</enum>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_14">
                         <property name="spacing">
                          <number>0</number>
                         </property>
                         <property name="leftMargin">
                          <number>0</number>
                         </property>
                         <property name="topMargin">
                          <number>0</number>
                         </property>
                         <property name="rightMargin">
                          <number>0</number>
                         </property>
                         <property name="bottomMargin">
                          <number>0</number>
                         </property>
                         <item>
                          <widget class="QPushButton" name="btn_message">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>45</height>
                            </size>
                           </property>
                           <property name="font">
                            <font>
                             <family>Segoe UI</family>
                             <pointsize>10</pointsize>
                             <italic>false</italic>
                             <bold>false</bold>
                            </font>
                           </property>
                           <property name="cursor">
                            <cursorShape>PointingHandCursor</cursorShape>
                           </property>
                           <property name="layoutDirection">
                            <enum>Qt::LeftToRight</enum>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">background-image: url(:/icons/images/icons/cil-envelope-open.png);</string>
                           </property>
                           <property name="text">
                            <string>Message</string>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QPushButton" name="btn_print">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>45</height>
                            </size>
                           </property>
                           <property name="font">
                            <font>
                             <family>Segoe UI</family>
                             <pointsize>10</pointsize>
                             <italic>false</italic>
                             <bold>false</bold>
                            </font>
                           </property>
                           <property name="cursor">
                            <cursorShape>PointingHandCursor</cursorShape>
                           </property>
                           <property name="layoutDirection">
                            <enum>Qt::LeftToRight</enum>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">background-image: url(:/icons/images/icons/cil-print.png);</string>
                           </property>
                           <property name="text">
                            <string>Print</string>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QPushButton" name="btn_logout">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>45</height>
                            </size>
                           </property>
                           <property name="font">
                            <font>
                             <family>Segoe UI</family>
                             <pointsize>10</pointsize>
                             <italic>false</italic>
                             <bold>false</bold>
                            </font>
                           </property>
                           <property name="cursor">
                            <cursorShape>PointingHandCursor</cursorShape>
                           </property>
                           <property name="layoutDirection">
                            <enum>Qt::LeftToRight</enum>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">background-image: url(:/icons/images/icons/cil-account-logout.png);</string>
                           </property>
                           <property name="text">
                            <string>Logout</string>
                           </property>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="bottomBar">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>22</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>********</width>
                 <height>22</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_5">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="creditsLabel">
                  <property name="maximumSize">
                   <size>
                    <width>********</width>
                    <height>16</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Segoe UI</family>
                    <pointsize>-1</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>By: Wanderson M. Pimenta</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="version">
                  <property name="text">
                   <string>v1.0.3</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QFrame" name="frame_size_grip">
                  <property name="minimumSize">
                   <size>
                    <width>20</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>********</height>
                   </size>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui>
